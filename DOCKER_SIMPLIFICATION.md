# Docker Setup Simplification

## Overview

This document describes the simplification of the Docker setup for e2e testing, eliminating the need for the `sentry-test-services` container while maintaining the same functionality and CI workflow structure.

## Changes Made

### 1. Docker Compose Configuration (`.devcontainer/docker-compose.yml`)

**Before:**
- `sentry-test-services`: Separate container for running foreman with mini apps
- `sentry-test`: Container for running tests, dependent on `sentry-test-services`

**After:**
- `sentry-test`: Single container that handles both running mini apps and executing tests
- Removed `sentry-test-services` container entirely
- Added port mappings (4000:4000, 4001:4001) to `sentry-test`
- Added `command: "foreman start"` to run mini apps by default

### 2. Run Script (`.devcontainer/run`)

**Before:**
- `test` service: Only installed main project dependencies
- `test-services` service: Installed main + rails-mini dependencies

**After:**
- `test` service: Now installs main + rails-mini + svelte-mini dependencies (same as `test-services` did)
- Removed `test-services` service option
- Updated help text to reflect available services

### 3. Environment Variables (`.devcontainer/.env.example`)

**Before:**
```
SENTRY_E2E_RAILS_APP_URL="http://sentry-test-services:4000"
SENTRY_E2E_SVELTE_APP_URL="http://sentry-test-services:4001"
```

**After:**
```
SENTRY_E2E_RAILS_APP_URL="http://localhost:4000"
SENTRY_E2E_SVELTE_APP_URL="http://localhost:4001"
```

### 4. CI Workflow (`.github/workflows/e2e_tests.yml`)

**Before:**
```yaml
- name: Set up test services
  run: docker compose run --rm sentry-test-services echo "Done"

- name: Start test services
  run: docker compose up -d sentry-test-services

- name: Set up sentry-test container
  run: docker compose run --rm sentry-test echo "Done"

- name: Run e2e tests via sentry-test
  run: docker compose run --rm sentry-test bundle exec rake
```

**After:**
```yaml
- name: Set up test container
  run: docker compose run --rm sentry-test echo "Done"

- name: Start test services
  run: docker compose up -d sentry-test

- name: Run e2e tests via sentry-test
  run: docker compose exec sentry-test bundle exec rake
```

## Benefits

1. **Simplified Architecture**: Single container instead of two separate containers
2. **Reduced Complexity**: Fewer moving parts to manage and debug
3. **Maintained Functionality**: All existing functionality preserved
4. **Same CI Structure**: Workflow steps remain logically separate for clarity
5. **Better Resource Usage**: No need for separate container just for running foreman

## How It Works

1. **Setup Phase**: The `sentry-test` container installs all dependencies (main project + mini apps)
2. **Service Phase**: Container starts with `foreman start` command, running both mini apps
3. **Test Phase**: Tests execute in the same container using `docker compose exec`
4. **Health Checks**: Mini apps expose health endpoints on ports 4000 and 4001 as before

## Validation

The changes have been tested and validated to ensure:
- Container setup works correctly
- Both mini apps start and respond to health checks
- E2e tests can be executed successfully and pass
- Environment variables correctly point to localhost instead of removed container
- All functionality matches the previous setup

## Migration Notes

- No changes needed for local development workflows
- CI pipeline maintains the same logical structure
- All environment variables and configurations remain the same
- Foreman Procfile unchanged
